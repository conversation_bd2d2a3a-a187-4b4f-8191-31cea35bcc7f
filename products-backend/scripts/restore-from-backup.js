#!/usr/bin/env node

/**
 * 数据库恢复脚本
 * 从JSON备份文件恢复MongoDB数据
 */

const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// 配置
const MONGODB_URI = process.env.MONGODB_URI;
const BACKUP_DIR = path.join(__dirname, '../backups/backup-2025-07-25T01-08-33');

// 恢复统计
const restoreStats = {
  products: { attempted: 0, success: 0, failed: 0 },
  images: { attempted: 0, success: 0, failed: 0 },
  synclogs: { attempted: 0, success: 0, failed: 0 },
  categories: { attempted: 0, success: 0, failed: 0 },
  totalTime: 0
};

async function connectDatabase() {
  try {
    console.log('🔌 连接到数据库...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ 数据库连接成功');
  } catch (error) {
    console.error('❌ 数据库连接失败:', error);
    throw error;
  }
}

async function restoreCollection(collectionName) {
  const filePath = path.join(BACKUP_DIR, `${collectionName}.json`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️ 备份文件不存在: ${filePath}`);
    return;
  }

  try {
    console.log(`\n📦 恢复集合: ${collectionName}`);
    
    // 读取备份数据
    const backupData = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    
    if (!Array.isArray(backupData) || backupData.length === 0) {
      console.log(`ℹ️ 集合 ${collectionName} 无数据需要恢复`);
      return;
    }

    console.log(`📊 发现 ${backupData.length} 个文档需要恢复`);
    restoreStats[collectionName].attempted = backupData.length;

    // 获取集合引用
    const collection = mongoose.connection.db.collection(collectionName);
    
    // 清空现有数据（如果有）
    await collection.deleteMany({});
    console.log(`🗑️ 清空现有数据`);

    // 批量插入数据
    const batchSize = 100;
    let successCount = 0;
    let failedCount = 0;

    for (let i = 0; i < backupData.length; i += batchSize) {
      const batch = backupData.slice(i, i + batchSize);
      
      try {
        // 处理ObjectId字段
        const processedBatch = batch.map(doc => {
          if (doc._id && typeof doc._id === 'string') {
            doc._id = new mongoose.Types.ObjectId(doc._id);
          }
          
          // 处理其他可能的ObjectId字段
          if (doc.productId && typeof doc.productId === 'string' && doc.productId.length === 24) {
            try {
              doc.productId = new mongoose.Types.ObjectId(doc.productId);
            } catch (e) {
              // 如果不是有效的ObjectId，保持原值
            }
          }
          
          return doc;
        });

        await collection.insertMany(processedBatch, { ordered: false });
        successCount += batch.length;
        
        if (i % (batchSize * 10) === 0) {
          console.log(`   进度: ${Math.min(i + batchSize, backupData.length)}/${backupData.length}`);
        }
      } catch (error) {
        console.error(`   ❌ 批次插入失败 (${i}-${i + batch.length}):`, error.message);
        failedCount += batch.length;
      }
    }

    restoreStats[collectionName].success = successCount;
    restoreStats[collectionName].failed = failedCount;

    console.log(`✅ 集合 ${collectionName} 恢复完成:`);
    console.log(`   - 成功: ${successCount}`);
    console.log(`   - 失败: ${failedCount}`);
    
  } catch (error) {
    console.error(`❌ 恢复集合 ${collectionName} 失败:`, error);
    restoreStats[collectionName].failed = restoreStats[collectionName].attempted;
  }
}

async function verifyRestore() {
  console.log('\n🔍 验证恢复结果...');
  
  const collections = ['products', 'images', 'synclogs'];
  
  for (const collName of collections) {
    try {
      const collection = mongoose.connection.db.collection(collName);
      const count = await collection.countDocuments();
      console.log(`📊 ${collName}: ${count} 个文档`);
    } catch (error) {
      console.error(`❌ 验证集合 ${collName} 失败:`, error.message);
    }
  }
}

async function generateReport() {
  console.log('\n📋 恢复报告:');
  console.log('='.repeat(50));
  
  let totalAttempted = 0;
  let totalSuccess = 0;
  let totalFailed = 0;
  
  Object.keys(restoreStats).forEach(collection => {
    if (collection !== 'totalTime') {
      const stats = restoreStats[collection];
      totalAttempted += stats.attempted;
      totalSuccess += stats.success;
      totalFailed += stats.failed;
      
      if (stats.attempted > 0) {
        console.log(`${collection}:`);
        console.log(`  尝试: ${stats.attempted}`);
        console.log(`  成功: ${stats.success}`);
        console.log(`  失败: ${stats.failed}`);
        console.log(`  成功率: ${((stats.success / stats.attempted) * 100).toFixed(2)}%`);
        console.log('');
      }
    }
  });
  
  console.log(`总计:`);
  console.log(`  尝试: ${totalAttempted}`);
  console.log(`  成功: ${totalSuccess}`);
  console.log(`  失败: ${totalFailed}`);
  console.log(`  总成功率: ${totalAttempted > 0 ? ((totalSuccess / totalAttempted) * 100).toFixed(2) : 0}%`);
  console.log(`  总耗时: ${(restoreStats.totalTime / 1000).toFixed(2)} 秒`);
}

async function main() {
  const startTime = Date.now();
  
  try {
    console.log('🚀 开始数据库恢复...');
    console.log(`📁 备份目录: ${BACKUP_DIR}`);
    
    await connectDatabase();
    
    // 按顺序恢复集合
    await restoreCollection('products');
    await restoreCollection('images');
    await restoreCollection('synclogs');
    await restoreCollection('categories');
    
    // 验证恢复结果
    await verifyRestore();
    
    restoreStats.totalTime = Date.now() - startTime;
    
    // 生成报告
    await generateReport();
    
    console.log('\n✨ 数据库恢复完成！');
    
  } catch (error) {
    console.error('❌ 恢复过程失败:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
  }
}

if (require.main === module) {
  main();
}

module.exports = { main, restoreCollection, verifyRestore };
